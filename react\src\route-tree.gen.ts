/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

import { Route as rootRouteImport } from './routes/__root'
import { Route as KnowledgeRouteImport } from './routes/knowledge'
import { Route as AssetsRouteImport } from './routes/assets'
import { Route as Agent_studioRouteImport } from './routes/agent_studio'
import { Route as IndexRouteImport } from './routes/index'
import { Route as CanvasIdRouteImport } from './routes/canvas.$id'

const KnowledgeRoute = KnowledgeRouteImport.update({
  id: '/knowledge',
  path: '/knowledge',
  getParentRoute: () => rootRouteImport,
} as any)
const AssetsRoute = AssetsRouteImport.update({
  id: '/assets',
  path: '/assets',
  getParentRoute: () => rootRouteImport,
} as any)
const Agent_studioRoute = Agent_studioRouteImport.update({
  id: '/agent_studio',
  path: '/agent_studio',
  getParentRoute: () => rootRouteImport,
} as any)
const IndexRoute = IndexRouteImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => rootRouteImport,
} as any)
const CanvasIdRoute = CanvasIdRouteImport.update({
  id: '/canvas/$id',
  path: '/canvas/$id',
  getParentRoute: () => rootRouteImport,
} as any)

export interface FileRoutesByFullPath {
  '/': typeof IndexRoute
  '/agent_studio': typeof Agent_studioRoute
  '/assets': typeof AssetsRoute
  '/knowledge': typeof KnowledgeRoute
  '/canvas/$id': typeof CanvasIdRoute
}
export interface FileRoutesByTo {
  '/': typeof IndexRoute
  '/agent_studio': typeof Agent_studioRoute
  '/assets': typeof AssetsRoute
  '/knowledge': typeof KnowledgeRoute
  '/canvas/$id': typeof CanvasIdRoute
}
export interface FileRoutesById {
  __root__: typeof rootRouteImport
  '/': typeof IndexRoute
  '/agent_studio': typeof Agent_studioRoute
  '/assets': typeof AssetsRoute
  '/knowledge': typeof KnowledgeRoute
  '/canvas/$id': typeof CanvasIdRoute
}
export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths: '/' | '/agent_studio' | '/assets' | '/knowledge' | '/canvas/$id'
  fileRoutesByTo: FileRoutesByTo
  to: '/' | '/agent_studio' | '/assets' | '/knowledge' | '/canvas/$id'
  id:
    | '__root__'
    | '/'
    | '/agent_studio'
    | '/assets'
    | '/knowledge'
    | '/canvas/$id'
  fileRoutesById: FileRoutesById
}
export interface RootRouteChildren {
  IndexRoute: typeof IndexRoute
  Agent_studioRoute: typeof Agent_studioRoute
  AssetsRoute: typeof AssetsRoute
  KnowledgeRoute: typeof KnowledgeRoute
  CanvasIdRoute: typeof CanvasIdRoute
}

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/knowledge': {
      id: '/knowledge'
      path: '/knowledge'
      fullPath: '/knowledge'
      preLoaderRoute: typeof KnowledgeRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/assets': {
      id: '/assets'
      path: '/assets'
      fullPath: '/assets'
      preLoaderRoute: typeof AssetsRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/agent_studio': {
      id: '/agent_studio'
      path: '/agent_studio'
      fullPath: '/agent_studio'
      preLoaderRoute: typeof Agent_studioRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/': {
      id: '/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof IndexRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/canvas/$id': {
      id: '/canvas/$id'
      path: '/canvas/$id'
      fullPath: '/canvas/$id'
      preLoaderRoute: typeof CanvasIdRouteImport
      parentRoute: typeof rootRouteImport
    }
  }
}

const rootRouteChildren: RootRouteChildren = {
  IndexRoute: IndexRoute,
  Agent_studioRoute: Agent_studioRoute,
  AssetsRoute: AssetsRoute,
  KnowledgeRoute: KnowledgeRoute,
  CanvasIdRoute: CanvasIdRoute,
}
export const routeTree = rootRouteImport
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()
