export const exampleMessages = [
  {
    role: "user",
    content: [
      {
        type: "text",
        text: "Can u post this marketing article [AI Marketing Agent Copilot 👋](https://www.reddit.com/r/AI_Agents/comments/1kj8nbu/bulk_auto_ai_video_creator/) to Medium.com",
      },
    ],
  },
  {
    role: "assistant",
    content: [
      {
        type: "text",
        text: "Sure! I'm happy to help you post your marketing post to Medium. I'll open the browser to do it.",
      },
    ],
  },
  // {
  //   role: "user",
  //   content: [
  //     {
  //       type: "text",
  //       text: "Can u search in reddit for relevant posts where people are asking about such product like my AI Marketing Agent Copilot and list 10 posts to best mention my product there!\n\n\n",
  //     },
  //   ],
  // },
  {
    role: "assistant",
    tool_calls: [
      {
        type: "function",
        id: "call_haZDmILRV90fD40lVNkkYIa6",
        function: {
          name: "browser_navigate",
          arguments: '{"url":"https://www.medium.com"}',
        },
      },
    ],
  },
  {
    role: "tool",
    tool_call_id: "call_haZDmILRV90fD40lVNkkYIa6",
    content:
      '- <PERSON><PERSON> code:\n```js\n// Navigate to https://www.reddit.com\nawait page.goto(\'https://www.reddit.com\');\n```\n\n- Page URL: https://www.reddit.com/\n- Page Title: Reddit - The heart of the internet\n- Page Snapshot\n```yaml\n- link "Skip to main content" [ref=s1e8]:\n  - /url: "#main-content"\n  - generic [ref=s1e10]: Skip to main content\n- generic [ref=s1e22]: Reddit - The heart of the internet\n- banner [ref=s1e45]:\n  - navigation [ref=s1e46]:\n    - link "Home" [ref=s1e59]:\n      - /url: /\n      - img [ref=s1e61]\n      - img [ref=s1e85]\n    - search [ref=s1e101]:\n      - textbox [ref=s1e107]\n    - link "Advertise on Reddit" [ref=s1e119]:\n      - /url: https://ads.reddit.com/register?utm_source=web3x_consumer&utm_name=nav_cta\n      - img [ref=s1e122]\n      - generic [ref=s1e124]:\n        - generic: Advertise on Reddit\n    - button "Open chat" [ref=s1e136]:\n      - img [ref=s1e139]\n      - generic [ref=s1e141]:\n        - generic: Open chat\n    - link "Create post" [ref=s1e152]:\n      - /url: /submit\n      - img [ref=s1e155]\n      - generic [ref=s1e157]: Create\n      - generic [ref=s1e158]:\n        - generic: Create post\n    - generic [ref=s1e161]:\n      - link "Open inbox" [ref=s1e167]:\n        - /url: /notifications\n        - img [ref=s1e170]\n        - generic [ref=s1e172]:\n          - generic: Open inbox\n      - generic:\n        - generic:\n          - generic:\n            - generic: "2"\n    - button "Expand user menu" [ref=s1e188]:\n      - img [ref=s1e199]\n      - generic [ref=s1e205]:\n        - generic: Expand user menu\n- link "Skip to Navigation" [ref=s1e255]:\n  - /url: "#left-sidebar-container"\n- link "Skip to Right Sidebar" [ref=s1e256]:\n  - /url: "#right-sidebar-container"\n- main [ref=s1e260]:\n  - \'button "Sort by: Best" [ref=s1e279]\':\n    - generic [ref=s1e281]: Best\n    - img [ref=s1e283]\n  - button "View:" [ref=s1e293]:\n    - img [ref=s1e296]\n    - img [ref=s1e299]\n  - separator [ref=s1e303]\n  - heading "Feed" [level=1] [ref=s1e327]\n  - article "Bulk Auto AI Video Creator" [ref=s1e330]:\n    - link "Bulk Auto AI Video Creator" [ref=s1e333]:\n      - /url: /r/AI_Agents/comments/1kj8nbu/bulk_auto_ai_video_creator/\n      - generic [ref=s1e334]:\n        - generic: Bulk Auto AI Video Creator\n    - link "r/AI_Agents icon r/AI_Agents" [ref=s1e344]:\n      - /url: /r/AI_Agents/\n      - img "r/AI_Agents icon" [ref=s1e348]\n      - generic [ref=s1e349]: r/AI_Agents\n    - generic [ref=s1e352]: •\n    - time [ref=s1e354]: 37 min. ago\n    - button "Open user actions" [ref=s1e362]:\n      - img [ref=s1e365]\n    - generic [ref=s1e373]:\n      - heading "Bulk Auto AI Video Creator" [level=2] [ref=s1e374]:\n        - link "Bulk Auto AI Video Creator" [ref=s1e376]:\n          - /url: /r/AI_Agents/comments/1kj8nbu/bulk_auto_ai_video_creator/\n          - text: Bulk Auto AI Video Creator\n      - generic:\n        - link "So there are obviously tools where you can feed it pictures and videos and they’ll piece something together. I am looking for something where you give it access to your phones gallery and it just goes to town creating reels based on day, location, etc. Yes I know this sounds ultra lazy, but I have like 10k media’s taken from my years of traveling. Only other option is to hire someone to go through my stuff and create, though that in itself would be invasive as personal info and non of rated stuff through my gallery lol. Would hope if there is a tool out there that they wouldn’t store or steal your data but would have to do research once I found one. If no such auto AI tool, then what’s the best (in your opinion) for making videos from 5-10 medias you feed it?":\n          - /url: /r/AI_Agents/comments/1kj8nbu/bulk_auto_ai_video_creator/\n          - generic:\n            - generic:\n              - paragraph: So there are obviously tools where you can feed it pictures and\n                  videos and they’ll piece something together. I am looking for\n                  something where you give it access to your phones gallery and\n                  it just goes to town creating reels based on day, location,\n                  etc.\n              - paragraph: Yes I know this sounds ultra lazy, but I have like 10k media’s taken\n                  from my years of traveling. Only other option is to hire\n                  someone to go through my stuff and create, though that in\n                  itself would be invasive as personal info and non of rated\n                  stuff through my gallery lol. Would hope if there is a tool\n                  out there that they wouldn’t store or steal your data but\n                  would have to do research once I found one.\n              - paragraph: If no such auto AI tool, then what’s the best (in your opinion) for\n                  making videos from 5-10 medias you feed it?\n    - button "Upvote" [ref=s1e403]:\n      - img [ref=s1e405]\n      - generic [ref=s1e407]:\n        - generic: Upvote\n    - generic [ref=s1e410]: "2"\n    - button "Downvote" [ref=s1e411]:\n      - img [ref=s1e413]\n      - generic [ref=s1e415]:\n        - generic: Downvote\n    - link "0 Go to comments" [ref=s1e417]:\n      - /url: /r/AI_Agents/comments/1kj8nbu/bulk_auto_ai_video_creator/\n      - generic [ref=s1e421]: "0"\n      - generic [ref=s1e422]:\n        - generic: Go to comments\n    - button "Give award" [ref=s1e425]\n    - button "Share Share" [ref=s1e432]:\n      - generic [ref=s1e435]: Share\n      - generic [ref=s1e436]:\n        - generic: Share\n  - separator [ref=s1e449]\n  - \'link "Advertisement: Torn City - World\'\'s Largest Text-based RPG" [ref=s1e455]\':\n    - /url: https://alb.reddit.com/cr?za=c44-mTC9pCxhjKpl85zLDzK3VVUydx8p4t56VVKJEpIaTd2uFAvs0yAsFHm6phNU5o1EvBwKBt_f8gG0-HRCo9UrjjXvHNxG_9JlA01Sb00iaxLUAKeJ6rdxbh0H3gmk3yPMzYoltCSTaNE314-VNXbW-OnvHL3brFkVaOwDNCuqtAI8N2nbrGBbGO7xTPUpSwPvddrKxxrsJn3eoH5ycSkxaWAtdSrKVdzLrwxOV6xB9jUOa1ocf3zPktE7iSY_uV-dsSL6F24Yrd0OpmUZ5V9_7AKpc0Waoal1GYsPrH2Wya3Lx84VP5mDfU768chBPfHUOxaxmh4zzsNWvi2quzyi1OsH-PZc-rwU9K4Wx6RvDSGB4dXzzH3kFyCIHluOV95ckebF_c7xtDHipOBxY0f_1C5nJDcws_rputTcYce63tOSP-PFF_J7A9AwNC5ybHfzSdtIKHh9qCUk9AX1xX0aUsENwAMmjCGG_Z8HUwboIvFyRdshDujdf06UZ9jikPPt-bi8CRvC14pv9GktqGjcY5OpXSq6Yy3I8NcDvnCM8gfcfgcMtLTZ&zp=VlLzGiIXlDpO2ysFkNwyI9zxY6znhwXYkWVELnnTBgWak3rFHK99VHj_4IQO7o_GuD2MIiB0G2tZyV_lUIDpWV0GgGw16_gWN841hkSnFNcg-rZ-5A8k756HrVk8H4-t1nVqDsfXIf42xY1ndmoOk-U3mW04qsC2wCnw_H681qy9c8bE0Xn1SgmH5gEmIXukFQboJLBr-CWUcysf72MFK9cWsxhtTf3gJ9xjl_6loSsqqYnqv4IUXHLa6qfaMQvvXKl6WNRnWXzi9TtPtlQlBKyobLeyGNz95QkWsfiTx_2Njjc5GOpeFpC2uZQAo7l5xdWVWknfrRYyYanFGiApWqlmcB-AtOphrl1J7qpT8erVDYkZlueOgAiysACRPBmkN016-TQsDBL_AbUhtD-hgo5HU3CV1XhqlPPVw9tueU8yR2HEq5GtmNdnDAPV0sqNQKKOG33iMF6cfzTDXOwz9vypJDgXDA\n  - link "u/TORNRPG avatar u/TORNRPG" [ref=s1e464]:\n    - /url: /user/TORNRPG/\n    - img "u/TORNRPG avatar" [ref=s1e469]\n    - generic [ref=s1e470]: u/TORNRPG\n  - generic [ref=s1e471]: •\n  - link "Promoted" [ref=s1e473]:\n    - /url: https://alb.reddit.com/cr?za=c44-mTC9pCxhjKpl85zLDzK3VVUydx8p4t56VVKJEpIaTd2uFAvs0yAsFHm6phNU5o1EvBwKBt_f8gG0-HRCo9UrjjXvHNxG_9JlA01Sb00iaxLUAKeJ6rdxbh0H3gmk3yPMzYoltCSTaNE314-VNXbW-OnvHL3brFkVaOwDNCuqtAI8N2nbrGBbGO7xTPUpSwPvddrKxxrsJn3eoH5ycSkxaWAtdSrKVdzLrwxOV6xB9jUOa1ocf3zPktE7iSY_uV-dsSL6F24Yrd0OpmUZ5V9_7AKpc0Waoal1GYsPrH2Wya3Lx84VP5mDfU768chBPfHUOxaxmh4zzsNWvi2quzyi1OsH-PZc-rwU9K4Wx6RvDSGB4dXzzH3kFyCIHluOV95ckebF_c7xtDHipOBxY0f_1C5nJDcws_rputTcYce63tOSP-PFF_J7A9AwNC5ybHfzSdtIKHh9qCUk9AX1xX0aUsENwAMmjCGG_Z8HUwboIvFyRdshDujdf06UZ9jikPPt-bi8CRvC14pv9GktqGjcY5OpXSq6Yy3I8NcDvnCM8gfcfgcMtLTZ&zp=VlLzGiIXlDpO2ysFkNwyI9zxY6znhwXYkWVELnnTBgWak3rFHK99VHj_4IQO7o_GuD2MIiB0G2tZyV_lUIDpWV0GgGw16_gWN841hkSnFNcg-rZ-5A8k756HrVk8H4-t1nVqDsfXIf42xY1ndmoOk-U3mW04qsC2wCnw_H681qy9c8bE0Xn1SgmH5gEmIXukFQboJLBr-CWUcysf72MFK9cWsxhtTf3gJ9xjl_6loSsqqYnqv4IUXHLa6qfaMQvvXKl6WNRnWXzi9TtPtlQlBKyobLeyGNz95QkWsfiTx_2Njjc5GOpeFpC2uZQAo7l5xdWVWknfrRYyYanFGiApWqlmcB-AtOphrl1J7qpT8erVDYkZlueOgAiysACRPBmkN016-TQsDBL_AbUhtD-hgo5HU3CV1XhqlPPVw9tueU8yR2HEq5GtmNdnDAPV0sqNQKKOG33iMF6cfzTDXOwz9vypJDgXDA\n    - generic [ref=s1e475]: Promoted\n  - link [ref=s1e477]:\n    - /url: https://alb.reddit.com/cr?za=c44-mTC9pCxhjKpl85zLDzK3VVUydx8p4t56VVKJEpIaTd2uFAvs0yAsFHm6phNU5o1EvBwKBt_f8gG0-HRCo9UrjjXvHNxG_9JlA01Sb00iaxLUAKeJ6rdxbh0H3gmk3yPMzYoltCSTaNE314-VNXbW-OnvHL3brFkVaOwDNCuqtAI8N2nbrGBbGO7xTPUpSwPvddrKxxrsJn3eoH5ycSkxaWAtdSrKVdzLrwxOV6xB9jUOa1ocf3zPktE7iSY_uV-dsSL6F24Yrd0OpmUZ5V9_7AKpc0Waoal1GYsPrH2Wya3Lx84VP5mDfU768chBPfHUOxaxmh4zzsNWvi2quzyi1OsH-PZc-rwU9K4Wx6RvDSGB4dXzzH3kFyCIHluOV95ckebF_c7xtDHipOBxY0f_1C5nJDcws_rputTcYce63tOSP-PFF_J7A9AwNC5ybHfzSdtIKHh9qCUk9AX1xX0aUsENwAMmjCGG_Z8HUwboIvFyRdshDujdf06UZ9jikPPt-bi8CRvC14pv9GktqGjcY5OpXSq6Yy3I8NcDvnCM8gfcfgcMtLTZ&zp=VlLzGiIXlDpO2ysFkNwyI9zxY6znhwXYkWVELnnTBgWak3rFHK99VHj_4IQO7o_GuD2MIiB0G2tZyV_lUIDpWV0GgGw16_gWN841hkSnFNcg-rZ-5A8k756HrVk8H4-t1nVqDsfXIf42xY1ndmoOk-U3mW04qsC2wCnw_H681qy9c8bE0Xn1SgmH5gEmIXukFQboJLBr-CWUcysf72MFK9cWsxhtTf3gJ9xjl_6loSsqqYnqv4IUXHLa6qfaMQvvXKl6WNRnWXzi9TtPtlQlBKyobLeyGNz95QkWsfiTx_2Njjc5GOpeFpC2uZQAo7l5xdWVWknfrRYyYanFGiApWqlmcB-AtOphrl1J7qpT8erVDYkZlueOgAiysACRPBmkN016-TQsDBL_AbUhtD-hgo5HU3CV1XhqlPPVw9tueU8yR2HEq5GtmNdnDAPV0sqNQKKOG33iMF6cfzTDXOwz9vypJDgXDA\n  - button "Open user actions" [ref=s1e485]:\n    - img [ref=s1e488]\n  - heading "Torn City - World\'s Largest Text-based RPG Free to Play — Be Anyone. Do Anything. Go Anywhere" [level=2] [ref=s1e496]:\n    - link "Torn City - World\'s Largest Text-based RPG" [ref=s1e500]:\n      - /url: https://alb.reddit.com/cr?za=c44-mTC9pCxhjKpl85zLDzK3VVUydx8p4t56VVKJEpIaTd2uFAvs0yAsFHm6phNU5o1EvBwKBt_f8gG0-HRCo9UrjjXvHNxG_9JlA01Sb00iaxLUAKeJ6rdxbh0H3gmk3yPMzYoltCSTaNE314-VNXbW-OnvHL3brFkVaOwDNCuqtAI8N2nbrGBbGO7xTPUpSwPvddrKxxrsJn3eoH5ycSkxaWAtdSrKVdzLrwxOV6xB9jUOa1ocf3zPktE7iSY_uV-dsSL6F24Yrd0OpmUZ5V9_7AKpc0Waoal1GYsPrH2Wya3Lx84VP5mDfU768chBPfHUOxaxmh4zzsNWvi2quzyi1OsH-PZc-rwU9K4Wx6RvDSGB4dXzzH3kFyCIHluOV95ckebF_c7xtDHipOBxY0f_1C5nJDcws_rputTcYce63tOSP-PFF_J7A9AwNC5ybHfzSdtIKHh9qCUk9AX1xX0aUsENwAMmjCGG_Z8HUwboIvFyRdshDujdf06UZ9jikPPt-bi8CRvC14pv9GktqGjcY5OpXSq6Yy3I8NcDvnCM8gfcfgcMtLTZ&zp=V...Content truncated to 8000 characters due to length limit',
  },
  {
    role: "tool",
    tool_call_id: "call_gQSenAJ1Cjfq7YraGb8JV8wJ",
    content:
      '- Ran Playwright code:\n```js\n// Fill "AI marketing tool" into "search box"\nawait page.getByLabel(\'\', { exact: true }).fill(\'AI marketing tool\');\n```\n\n- Page URL: https://www.reddit.com/\n- Page Title: Reddit - The heart of the internet\n- Page Snapshot\n```yaml\n- link "Skip to main content" [ref=s2e8]:\n  - /url: "#main-content"\n  - generic [ref=s2e10]: Skip to main content\n- generic [ref=s2e22]: Reddit - The heart of the internet\n- banner [ref=s2e45]:\n  - navigation [ref=s2e46]:\n    - link "Home" [ref=s2e59]:\n      - /url: /\n      - img [ref=s2e61]\n      - img [ref=s2e85]\n    - search [ref=s2e101]:\n      - textbox "Clear search" [ref=s2e107]: AI marketing tool\n      - button "Clear search" [ref=s2e110]:\n        - img [ref=s2e113]\n        - generic [ref=s2e115]:\n          - generic: Clear search\n    - menu [ref=s2e118]:\n      - menuitem "ai marketing tool" [ref=s2e122]:\n        - img [ref=s2e125]\n        - generic [ref=s2e130]: ai marketing tool\n      - menuitem "ai marketing tools" [ref=s2e137]:\n        - img [ref=s2e140]\n        - generic [ref=s2e144]:\n          - generic [ref=s2e145]: ai marketing tool\n          - text: s\n      - generic [ref=s2e149]: Communities\n      - menuitem "r/aimarketing 2.4K members" [ref=s2e153]:\n        - generic [ref=s2e161]: r/aimarketing\n        - generic [ref=s2e163]: 2.4K members\n      - menuitem "r/AIToolTracker 3.5K members" [ref=s2e167]:\n        - generic [ref=s2e175]: r/AIToolTracker\n        - generic [ref=s2e177]: 3.5K members\n      - menuitem "r/AI_tool 193 members" [ref=s2e181]:\n        - generic [ref=s2e189]: r/AI_tool\n        - generic [ref=s2e191]: 193 members\n      - menuitem "r/content_marketing 141K members" [ref=s2e195]:\n        - generic [ref=s2e203]: r/content_marketing\n        - generic [ref=s2e205]: 141K members\n      - menuitem "r/PopularAiTool 566 members" [ref=s2e209]:\n        - generic [ref=s2e217]: r/PopularAiTool\n        - generic [ref=s2e219]: 566 members\n    - link "Advertise on Reddit" [ref=s2e230]:\n      - /url: https://ads.reddit.com/register?utm_source=web3x_consumer&utm_name=nav_cta\n      - img [ref=s2e233]\n      - generic [ref=s2e235]:\n        - generic: Advertise on Reddit\n    - button "Open chat" [ref=s2e247]:\n      - img [ref=s2e250]\n      - generic [ref=s2e252]:\n        - generic: Open chat\n    - link "Create post" [ref=s2e263]:\n      - /url: /submit\n      - img [ref=s2e266]\n      - generic [ref=s2e268]: Create\n      - generic [ref=s2e269]:\n        - generic: Create post\n    - generic [ref=s2e272]:\n      - link "Open inbox" [ref=s2e278]:\n        - /url: /notifications\n        - img [ref=s2e281]\n        - generic [ref=s2e283]:\n          - generic: Open inbox\n      - generic:\n        - generic:\n          - generic:\n            - generic: "2"\n    - button "Expand user menu" [ref=s2e299]:\n      - img [ref=s2e310]\n      - generic [ref=s2e316]:\n        - generic: Expand user menu\n- link "Skip to Navigation" [ref=s2e366]:\n  - /url: "#left-sidebar-container"\n- link "Skip to Right Sidebar" [ref=s2e367]:\n  - /url: "#right-sidebar-container"\n- main [ref=s2e371]:\n  - \'button "Sort by: Best" [ref=s2e390]\':\n    - generic [ref=s2e392]: Best\n    - img [ref=s2e394]\n  - button "View:" [ref=s2e404]:\n    - img [ref=s2e407]\n    - img [ref=s2e410]\n  - separator [ref=s2e414]\n  - heading "Feed" [level=1] [ref=s2e438]\n  - article "Bulk Auto AI Video Creator" [ref=s2e441]:\n    - link "Bulk Auto AI Video Creator" [ref=s2e444]:\n      - /url: /r/AI_Agents/comments/1kj8nbu/bulk_auto_ai_video_creator/\n      - generic [ref=s2e445]:\n        - generic: Bulk Auto AI Video Creator\n    - link "r/AI_Agents icon r/AI_Agents" [ref=s2e455]:\n      - /url: /r/AI_Agents/\n      - img "r/AI_Agents icon" [ref=s2e459]\n      - generic [ref=s2e460]: r/AI_Agents\n    - generic [ref=s2e463]: •\n    - time [ref=s2e465]: 37 min. ago\n    - button "Open user actions" [ref=s2e473]:\n      - img [ref=s2e476]\n    - generic [ref=s2e484]:\n      - heading "Bulk Auto AI Video Creator" [level=2] [ref=s2e485]:\n        - link "Bulk Auto AI Video Creator" [ref=s2e487]:\n          - /url: /r/AI_Agents/comments/1kj8nbu/bulk_auto_ai_video_creator/\n          - text: Bulk Auto AI Video Creator\n      - generic:\n        - link "So there are obviously tools where you can feed it pictures and videos and they’ll piece something together. I am looking for something where you give it access to your phones gallery and it just goes to town creating reels based on day, location, etc. Yes I know this sounds ultra lazy, but I have like 10k media’s taken from my years of traveling. Only other option is to hire someone to go through my stuff and create, though that in itself would be invasive as personal info and non of rated stuff through my gallery lol. Would hope if there is a tool out there that they wouldn’t store or steal your data but would have to do research once I found one. If no such auto AI tool, then what’s the best (in your opinion) for making videos from 5-10 medias you feed it?":\n          - /url: /r/AI_Agents/comments/1kj8nbu/bulk_auto_ai_video_creator/\n          - generic:\n            - generic:\n              - paragraph: So there are obviously tools where you can feed it pictures and\n                  videos and they’ll piece something together. I am looking for\n                  something where you give it access to your phones gallery and\n                  it just goes to town creating reels based on day, location,\n                  etc.\n              - paragraph: Yes I know this sounds ultra lazy, but I have like 10k media’s taken\n                  from my years of traveling. Only other option is to hire\n                  someone to go through my stuff and create, though that in\n                  itself would be invasive as personal info and non of rated\n                  stuff through my gallery lol. Would hope if there is a tool\n                  out there that they wouldn’t store or steal your data but\n                  would have to do research once I found one.\n              - paragraph: If no such auto AI tool, then what’s the best (in your opinion) for\n                  making videos from 5-10 medias you feed it?\n    - button "Upvote" [ref=s2e514]:\n      - img [ref=s2e516]\n      - generic [ref=s2e518]:\n        - generic: Upvote\n    - generic [ref=s2e521]: "2"\n    - button "Downvote" [ref=s2e522]:\n      - img [ref=s2e524]\n      - generic [ref=s2e526]:\n        - generic: Downvote\n    - link "0 Go to comments" [ref=s2e528]:\n      - /url: /r/AI_Agents/comments/1kj8nbu/bulk_auto_ai_video_creator/\n      - generic [ref=s2e532]: "0"\n      - generic [ref=s2e533]:\n        - generic: Go to comments\n    - button "Give award" [ref=s2e536]\n    - button "Share Share" [ref=s2e543]:\n      - generic [ref=s2e546]: Share\n      - generic [ref=s2e547]:\n        - generic: Share\n  - separator [ref=s2e560]\n  - \'link "Advertisement: Torn City - World\'\'s Largest Text-based RPG" [ref=s2e566]\':\n    - /url: https://alb.reddit.com/cr?za=c44-mTC9pCxhjKpl85zLDzK3VVUydx8p4t56VVKJEpIaTd2uFAvs0yAsFHm6phNU5o1EvBwKBt_f8gG0-HRCo9UrjjXvHNxG_9JlA01Sb00iaxLUAKeJ6rdxbh0H3gmk3yPMzYoltCSTaNE314-VNXbW-OnvHL3brFkVaOwDNCuqtAI8N2nbrGBbGO7xTPUpSwPvddrKxxrsJn3eoH5ycSkxaWAtdSrKVdzLrwxOV6xB9jUOa1ocf3zPktE7iSY_uV-dsSL6F24Yrd0OpmUZ5V9_7AKpc0Waoal1GYsPrH2Wya3Lx84VP5mDfU768chBPfHUOxaxmh4zzsNWvi2quzyi1OsH-PZc-rwU9K4Wx6RvDSGB4dXzzH3kFyCIHluOV95ckebF_c7xtDHipOBxY0f_1C5nJDcws_rputTcYce63tOSP-PFF_J7A9AwNC5ybHfzSdtIKHh9qCUk9AX1xX0aUsENwAMmjCGG_Z8HUwboIvFyRdshDujdf06UZ9jikPPt-bi8CRvC14pv9GktqGjcY5OpXSq6Yy3I8NcDvnCM8gfcfgcMtLTZ&zp=VlLzGiIXlDpO2ysFkNwyI9zxY6znhwXYkWVELnnTBgWak3rFHK99VHj_4IQO7o_GuD2MIiB0G2tZyV_lUIDpWV0GgGw16_gWN841hkSnFNcg-rZ-5A8k756HrVk8H4-t1nVqDsfXIf42xY1ndmoOk-U3mW04qsC2wCnw_H681qy9c8bE0Xn1SgmH5gEmIXukFQboJLBr-CWUcysf72MFK9cWsxhtTf3gJ9xjl_6loSsqqYnqv4IUXHLa6qfaMQvvXKl6WNRnWXzi9TtPtlQlBKyobLeyGNz95QkWsfiTx_2Njjc5GOpeFpC2uZQAo7l5xdWVWknfrRYyYanFGiApWqlmcB-AtOphrl1J7qpT8erVDYkZlueOgAiysACRPBmkN016-TQsDBL_AbUhtD-hgo5HU3CV1XhqlPPVw9tueU8yR2HEq5GtmNdnDAPV0sqNQKKOG33iMF6cfzTDXOwz9vypJDgXDA\n  - link "u/TORNRPG avatar u/TORNRPG" [ref=s2e575]:\n    - /url: /user/TORNRPG/\n    - img "u/TORNRPG avatar" [ref=s2e580]\n    - generic [ref=s2e581]: u/TORNRPG\n  - generic [ref=s2e582]: •\n  - link "Promoted" [ref=s2e584]:\n    - /url: https://alb.reddit.com/cr?za=c44-mTC9pCxhjKpl85zLDzK3VVUydx8p4t56VVKJEpIaTd2uFAvs0yAsFHm6phNU5o1EvBwKBt_f8gG0-HRCo9UrjjXvHNxG_9JlA01Sb00iaxLUAKeJ6rdxbh0H3gmk3yPMzYoltCSTaNE314-VNXbW-OnvHL3brFkVaOwDNCuqtAI8N2nbrGBbGO7xTPUpSwPvddrKxxrsJn3eoH5ycSkxaWAtdSrKVdzLrwxOV6xB9jUOa1ocf3zPktE7iSY_uV-dsSL6F24Yrd0OpmUZ5V9_7AKpc0Waoal1GYsPrH2Wya3Lx84VP5mDfU768chBPfHUOxaxmh4zzsNWvi2quzyi1OsH-PZc-rwU9K4Wx6RvDSGB4dXzzH3kFyCIHluOV95ckebF_c7xtDHipOBxY0f_1C5nJDcws_rputTcYce63tOSP-PFF_J7A9AwNC5ybHfzSdtIKHh9qCUk9AX1xX0aUsENwAMmjCGG_Z8HUwboIvFyRdshDujdf06UZ9jikPPt-bi8CRvC14pv9GktqGjcY5OpXSq6Yy3I8NcDvnCM8gfcfgcMtLTZ&zp=VlLzGiIXlDpO2ysFkNwyI9zxY6znhwXYkWVELnnTBgWak3rFHK99VHj_4IQO7o_GuD2MIiB0G2tZyV_lUIDpWV0GgGw16_gWN841hkSnFNcg-rZ-5A8k756HrVk8H4-t1nVqDsfXIf42xY1ndmoOk-U3mW04qsC2wCnw_H681qy9c8bE0Xn1SgmH5gEmIXukFQboJLBr-CWUcysf72MFK9cWsxhtTf3gJ9xjl_6loSsqqYnqv4IUXHLa6qfaMQvvXKl6WNRnWXzi9TtPtlQlBKyobLeyGNz95QkWsfiTx_2Njjc5GOpeFpC2uZQAo7l5xdWVWknfrRYyYanFGiApWqlmcB-AtOphrl1J7qpT8erVDYkZlueOgAiysACRPBmkN016-TQsDBL_AbUhtD-hgo5HU3CV1XhqlPPVw9tueU8yR2HEq5GtmNdnDAPV0sqNQKKOG33iMF6cfzTDXOwz9vypJDgXDA\n    - generic [ref=s2e586]: Promoted\n  - link [ref=s2e588]:\n    - /url: https://alb.reddit.com/cr?za=c44-mTC9pCxhjKpl85zLDzK3VVUydx8p4t56VVKJEpIaTd2uFAvs0yAsFHm6phNU5o1EvBwKBt_f8gG0-HRCo9UrjjXvHNxG_9JlA01Sb00iaxLUAKeJ6rdxbh0H3gmk3yPMzYoltCSTaNE314-VNXbW-OnvHL3brFkVaOwDNCuqtAI8N2nbrGBbGO7xTPUpSwPvddrKxxrsJn3eoH5ycSkxaWAtdSrKVdzLrwxOV6xB9jUOa1ocf3zPktE7iSY_uV-dsSL6F24Yrd0OpmUZ5V9_7AKpc0Waoal1GYsPrH2Wya3Lx84VP5mDfU768chBPfHUOxaxmh4zzsNWvi2quzyi1OsH-PZc-rwU9K4Wx6RvDSGB4dXzzH3kFyCIHluOV95ckebF_c7xtDHipOBxY0f_1C5nJDcws_rputTcYce63tOSP-PFF_J7A9AwNC5ybHfzSdtIKHh9qCUk9AX1xX0aUsENwAMmjCGG_Z8HUwboIvFyRdshDujdf06UZ9jikPPt-bi8CRvC14pv9GktqGjcY5OpXSq6Yy3I8NcDvnCM8gfcfgcMtLTZ&zp=VlLzGiIXlDpO2ysFkN...Content truncated to 8000 characters due to length limit',
  },
  {
    role: "assistant",
    tool_calls: [
      {
        type: "function",
        id: "call_xr8RR9AJq5HSydbnP40k9Qgp",
        function: {
          name: "browser_press_key",
          arguments: '{"key":"Enter"}',
        },
      },
    ],
  },
  {
    role: "tool",
    tool_call_id: "call_xr8RR9AJq5HSydbnP40k9Qgp",
    content:
      '- Ran Playwright code:\n```js\n// Press Enter\nawait page.keyboard.press(\'Enter\');\n```\n\n- Page URL: https://www.reddit.com/search/?q=AI+marketing+tool&cId=76c4ee55-d828-4b32-bf15-288533d53843&iId=24b96aee-a2c9-4c2a-a1be-b1f8bf4d6ff0\n- Page Title: Reddit - The heart of the internet\n- Page Snapshot\n```yaml\n- link "Skip to main content" [ref=s3e8]:\n  - /url: "#main-content"\n  - generic [ref=s3e10]: Skip to main content\n- generic [ref=s3e22]: Reddit - The heart of the internet\n- banner [ref=s3e45]:\n  - navigation [ref=s3e46]:\n    - link "Home" [ref=s3e59]:\n      - /url: /\n      - img [ref=s3e61]\n      - img [ref=s3e85]\n    - search [ref=s3e101]:\n      - textbox "Clear search" [ref=s3e107]: AI marketing tool\n      - button "Clear search" [ref=s3e110]:\n        - img [ref=s3e113]\n        - generic [ref=s3e115]:\n          - generic: Clear search\n    - menu [ref=s3e118]:\n      - menuitem "ai marketing tool" [ref=s3e122]:\n        - img [ref=s3e125]\n        - generic [ref=s3e130]: ai marketing tool\n      - menuitem "ai marketing tools" [ref=s3e137]:\n        - img [ref=s3e140]\n        - generic [ref=s3e144]:\n          - generic [ref=s3e145]: ai marketing tool\n          - text: s\n      - generic [ref=s3e149]: Communities\n      - menuitem "r/aimarketing 2.4K members" [ref=s3e153]:\n        - generic [ref=s3e161]: r/aimarketing\n        - generic [ref=s3e163]: 2.4K members\n      - menuitem "r/AIToolTracker 3.5K members" [ref=s3e167]:\n        - generic [ref=s3e175]: r/AIToolTracker\n        - generic [ref=s3e177]: 3.5K members\n      - menuitem "r/AI_tool 193 members" [ref=s3e181]:\n        - generic [ref=s3e189]: r/AI_tool\n        - generic [ref=s3e191]: 193 members\n      - menuitem "r/content_marketing 141K members" [ref=s3e195]:\n        - generic [ref=s3e203]: r/content_marketing\n        - generic [ref=s3e205]: 141K members\n      - menuitem "r/PopularAiTool 566 members" [ref=s3e209]:\n        - generic [ref=s3e217]: r/PopularAiTool\n        - generic [ref=s3e219]: 566 members\n    - link "Advertise on Reddit" [ref=s3e230]:\n      - /url: https://ads.reddit.com/register?utm_source=web3x_consumer&utm_name=nav_cta\n      - img [ref=s3e233]\n      - generic [ref=s3e235]:\n        - generic: Advertise on Reddit\n    - button "Open chat" [ref=s3e247]:\n      - img [ref=s3e250]\n      - generic [ref=s3e252]:\n        - generic: Open chat\n    - link "Create post" [ref=s3e263]:\n      - /url: /submit\n      - img [ref=s3e266]\n      - generic [ref=s3e268]: Create\n      - generic [ref=s3e269]:\n        - generic: Create post\n    - generic [ref=s3e272]:\n      - link "Open inbox" [ref=s3e278]:\n        - /url: /notifications\n        - img [ref=s3e281]\n        - generic [ref=s3e283]:\n          - generic: Open inbox\n      - generic:\n        - generic:\n          - generic:\n            - generic: "2"\n    - button "Expand user menu" [ref=s3e299]:\n      - img [ref=s3e310]\n      - generic [ref=s3e316]:\n        - generic: Expand user menu\n- link "Skip to Navigation" [ref=s3e366]:\n  - /url: "#left-sidebar-container"\n- link "Skip to Right Sidebar" [ref=s3e367]:\n  - /url: "#right-sidebar-container"\n- main [ref=s3e371]:\n  - \'button "Sort by: Best" [ref=s3e390]\':\n    - generic [ref=s3e392]: Best\n    - img [ref=s3e394]\n  - button "View:" [ref=s3e404]:\n    - img [ref=s3e407]\n    - img [ref=s3e410]\n  - separator [ref=s3e414]\n  - heading "Feed" [level=1] [ref=s3e438]\n  - article "Bulk Auto AI Video Creator" [ref=s3e441]:\n    - link "Bulk Auto AI Video Creator" [ref=s3e444]:\n      - /url: /r/AI_Agents/comments/1kj8nbu/bulk_auto_ai_video_creator/\n      - generic [ref=s3e445]:\n        - generic: Bulk Auto AI Video Creator\n    - link "r/AI_Agents icon r/AI_Agents" [ref=s3e455]:\n      - /url: /r/AI_Agents/\n      - img "r/AI_Agents icon" [ref=s3e459]\n      - generic [ref=s3e460]: r/AI_Agents\n    - generic [ref=s3e463]: •\n    - time [ref=s3e465]: 37 min. ago\n    - button "Open user actions" [ref=s3e473]:\n      - img [ref=s3e476]\n    - generic [ref=s3e484]:\n      - heading "Bulk Auto AI Video Creator" [level=2] [ref=s3e485]:\n        - link "Bulk Auto AI Video Creator" [ref=s3e487]:\n          - /url: /r/AI_Agents/comments/1kj8nbu/bulk_auto_ai_video_creator/\n          - text: Bulk Auto AI Video Creator\n      - generic:\n        - link "So there are obviously tools where you can feed it pictures and videos and they’ll piece something together. I am looking for something where you give it access to your phones gallery and it just goes to town creating reels based on day, location, etc. Yes I know this sounds ultra lazy, but I have like 10k media’s taken from my years of traveling. Only other option is to hire someone to go through my stuff and create, though that in itself would be invasive as personal info and non of rated stuff through my gallery lol. Would hope if there is a tool out there that they wouldn’t store or steal your data but would have to do research once I found one. If no such auto AI tool, then what’s the best (in your opinion) for making videos from 5-10 medias you feed it?":\n          - /url: /r/AI_Agents/comments/1kj8nbu/bulk_auto_ai_video_creator/\n          - generic:\n            - generic:\n              - paragraph: So there are obviously tools where you can feed it pictures and\n                  videos and they’ll piece something together. I am looking for\n                  something where you give it access to your phones gallery and\n                  it just goes to town creating reels based on day, location,\n                  etc.\n              - paragraph: Yes I know this sounds ultra lazy, but I have like 10k media’s taken\n                  from my years of traveling. Only other option is to hire\n                  someone to go through my stuff and create, though that in\n                  itself would be invasive as personal info and non of rated\n                  stuff through my gallery lol. Would hope if there is a tool\n                  out there that they wouldn’t store or steal your data but\n                  would have to do research once I found one.\n              - paragraph: If no such auto AI tool, then what’s the best (in your opinion) for\n                  making videos from 5-10 medias you feed it?\n    - button "Upvote" [ref=s3e514]:\n      - img [ref=s3e516]\n      - generic [ref=s3e518]:\n        - generic: Upvote\n    - generic [ref=s3e521]: "2"\n    - button "Downvote" [ref=s3e522]:\n      - img [ref=s3e524]\n      - generic [ref=s3e526]:\n        - generic: Downvote\n    - link "0 Go to comments" [ref=s3e528]:\n      - /url: /r/AI_Agents/comments/1kj8nbu/bulk_auto_ai_video_creator/\n      - generic [ref=s3e532]: "0"\n      - generic [ref=s3e533]:\n        - generic: Go to comments\n    - button "Give award" [ref=s3e536]\n    - button "Share Share" [ref=s3e543]:\n      - generic [ref=s3e546]: Share\n      - generic [ref=s3e547]:\n        - generic: Share\n  - separator [ref=s3e560]\n  - \'link "Advertisement: Torn City - World\'\'s Largest Text-based RPG" [ref=s3e566]\':\n    - /url: https://alb.reddit.com/cr?za=c44-mTC9pCxhjKpl85zLDzK3VVUydx8p4t56VVKJEpIaTd2uFAvs0yAsFHm6phNU5o1EvBwKBt_f8gG0-HRCo9UrjjXvHNxG_9JlA01Sb00iaxLUAKeJ6rdxbh0H3gmk3yPMzYoltCSTaNE314-VNXbW-OnvHL3brFkVaOwDNCuqtAI8N2nbrGBbGO7xTPUpSwPvddrKxxrsJn3eoH5ycSkxaWAtdSrKVdzLrwxOV6xB9jUOa1ocf3zPktE7iSY_uV-dsSL6F24Yrd0OpmUZ5V9_7AKpc0Waoal1GYsPrH2Wya3Lx84VP5mDfU768chBPfHUOxaxmh4zzsNWvi2quzyi1OsH-PZc-rwU9K4Wx6RvDSGB4dXzzH3kFyCIHluOV95ckebF_c7xtDHipOBxY0f_1C5nJDcws_rputTcYce63tOSP-PFF_J7A9AwNC5ybHfzSdtIKHh9qCUk9AX1xX0aUsENwAMmjCGG_Z8HUwboIvFyRdshDujdf06UZ9jikPPt-bi8CRvC14pv9GktqGjcY5OpXSq6Yy3I8NcDvnCM8gfcfgcMtLTZ&zp=VlLzGiIXlDpO2ysFkNwyI9zxY6znhwXYkWVELnnTBgWak3rFHK99VHj_4IQO7o_GuD2MIiB0G2tZyV_lUIDpWV0GgGw16_gWN841hkSnFNcg-rZ-5A8k756HrVk8H4-t1nVqDsfXIf42xY1ndmoOk-U3mW04qsC2wCnw_H681qy9c8bE0Xn1SgmH5gEmIXukFQboJLBr-CWUcysf72MFK9cWsxhtTf3gJ9xjl_6loSsqqYnqv4IUXHLa6qfaMQvvXKl6WNRnWXzi9TtPtlQlBKyobLeyGNz95QkWsfiTx_2Njjc5GOpeFpC2uZQAo7l5xdWVWknfrRYyYanFGiApWqlmcB-AtOphrl1J7qpT8erVDYkZlueOgAiysACRPBmkN016-TQsDBL_AbUhtD-hgo5HU3CV1XhqlPPVw9tueU8yR2HEq5GtmNdnDAPV0sqNQKKOG33iMF6cfzTDXOwz9vypJDgXDA\n  - link "u/TORNRPG avatar u/TORNRPG" [ref=s3e575]:\n    - /url: /user/TORNRPG/\n    - img "u/TORNRPG avatar" [ref=s3e580]\n    - generic [ref=s3e581]: u/TORNRPG\n  - generic [ref=s3e582]: •\n  - link "Promoted" [ref=s3e584]:\n    - /url: https://alb.reddit.com/cr?za=c44-mTC9pCxhjKpl85zLDzK3VVUydx8p4t56VVKJEpIaTd2uFAvs0yAsFHm6phNU5o1EvBwKBt_f8gG0-HRCo9UrjjXvHNxG_9JlA01Sb00iaxLUAKeJ6rdxbh0H3gmk3yPMzYoltCSTaNE314-VNXbW-OnvHL3brFkVaOwDNCuqtAI8N2nbrGBbGO7xTPUpSwPvddrKxxrsJn3eoH5ycSkxaWAtdSrKVdzLrwxOV6xB9jUOa1ocf3zPktE7iSY_uV-dsSL6F24Yrd0OpmUZ5V9_7AKpc0Waoal1GYsPrH2Wya3Lx84VP5mDfU768chBPfHUOxaxmh4zzsNWvi2quzyi1OsH-PZc-rwU9K4Wx6RvDSGB4dXzzH3kFyCIHluOV95ckebF_c7xtDHipOBxY0f_1C5nJDcws_rputTcYce63tOSP-PFF_J7A9AwNC5ybHfzSdtIKHh9qCUk9AX1xX0aUsENwAMmjCGG_Z8HUwboIvFyRdshDujdf06UZ9jikPPt-bi8CRvC14pv9GktqGjcY5OpXSq6Yy3I8NcDvnCM8gfcfgcMtLTZ&zp=VlLzGiIXlDpO2ysFkNwyI9zxY6znhwXYkWVELnnTBgWak3rFHK99VHj_4IQO7o_GuD2MIiB0G2tZyV_lUIDpWV0GgGw16_gWN841hkSnFNcg-rZ-5A8k756HrVk8H4-t1nVqDsfXIf42xY1ndmoOk-U3mW04qsC2wCnw_H681qy9c8bE0Xn1SgmH5gEmIXukFQboJLBr-CWUcysf72MFK9cWsxhtTf3gJ9xjl_6loSsqqYnqv4IUXHLa6qfaMQvvXKl6WNRnWXzi9TtPtlQlBKyobLeyGNz95QkWsfiTx_2Njjc5GOpeFpC2uZQAo7l5xdWVWknfrRYyYanFGiApWqlmcB-AtOphrl1J7qpT8erVDYkZlueOgAiysACRPBmkN016-TQsDBL_AbUhtD-hgo5HU3CV1XhqlPPVw9tueU8yR2HEq5GtmNdnDAPV0sqNQKKOG33iMF6cfzTDXOwz9vypJDgXDA\n    - generic [ref=s3e586]: Promoted\n  - link [ref=s3e588]:\n    - /url: https://alb.reddit.com/cr?za=c44-mTC9pCxhjKpl85zLDzK3VVUydx8p4t56VVKJEpIaTd2uFAvs0yAsFHm6phNU5o1EvBwKBt_f8gG0-HRCo9UrjjXvHNxG_9JlA01Sb00iaxLUAKeJ6rdxbh0H3gmk3yPMzYoltCSTaNE314-VNXbW-OnvHL3brFkVaOwDNCuqtAI8N2nbrGBbGO7xTPUpSwPvddrKxxrsJn3eoH5ycSkxaWAtdSrKVdzLrwxOV6xB9jUOa1ocf3zPktE7iSY_uV-dsSL6F24Yrd0OpmUZ5V9_7AKpc0Waoal1GYsPrH2Wya3Lx84VP5mDfU768chBPfHUOxaxmh4zzsNWvi2quzyi1OsH-PZc-rwU9K4Wx6RvDSGB4dXzzH3kFyCIHluOV95ckebF_c7xtDHipOBxY0f_1C5nJDcws_rputTcYce63tOSP-PFF_J7A9AwNC5ybHfzSdtIKHh9qCUk9AX1xX0aUsENwAMmjCGG_Z8HUwboIvFyRdshDujdf06UZ9jikPPt-bi8CRvC14pv9GktqGjcY5OpXSq6Yy...Content truncated to 8000 characters due to length limit',
  },
  {
    role: "assistant",
    tool_calls: [
      {
        type: "function",
        id: "call_haZDmILRV90fD40lVNkkYIa6",
        function: {
          name: "browser_click",
          arguments: '{"element":"input"}',
        },
      },
    ],
  },
  {
    role: "assistant",
    tool_calls: [
      {
        type: "function",
        id: "call_haZDmILRV90fD40lVNkkYIa6",
        function: {
          name: "browser_type",
          arguments:
            '{"text":"AI Marketing Agent Copilot 👋 \n this is a great new product we are working at "}',
        },
      },
    ],
  },
  {
    role: "assistant",
    tool_calls: [
      {
        type: "function",
        id: "call_gQSenAJ1Cjfq7YraGb8JV8wJ",
        function: {
          name: "prompt_user_single_choice",
          arguments:
            '{"element":"search box","ref":"s1e107","text":"AI marketing tool"}',
        },
      },
    ],
  },
  // {
  //   role: "assistant",
  //   content: [
  //     {
  //       type: "text",
  //       text: "Here are **10 Reddit posts where people might be interested** in your AI marketing tool, choose the ones you want to generate a reply:\n\n ",
  //     },
  //   ],
  // },
  // {
  //   role: "assistant",
  //   tool_calls: [
  //     {
  //       type: "function",
  //       id: "call_gQSenAJ1Cjfq7YraGb8JV8wJ",
  //       function: {
  //         name: "prompt_user_multi_choice",
  //         arguments:
  //           '{"element":"search box","ref":"s1e107","text":"AI marketing tool"}',
  //       },
  //     },
  //   ],
  // },
];
