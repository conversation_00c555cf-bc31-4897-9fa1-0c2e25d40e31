fastapi
uvicorn[standard]
anthropic
mcp
toml
pyinstaller
openai
ollama
httpx
aiohttp
gunicorn
aiosqlite
requests
Pillow
nanoid
python-multipart
aiofiles
certifi
websocket-client # needed for comfyui execution
rich # needed for comfyui execution
typer # needed for comfyui execution
langgraph==0.4.8
langgraph-checkpoint==2.0.26
langgraph-prebuilt==0.2.2
langgraph-sdk==0.1.70
langgraph-swarm==0.0.11
langchain-ollama==0.3.3
langchain-openai==0.3.21
python-socketio==5.13.0
pymediainfo
openai-agents
socksio # For vpn from command line like export https_proxy=http://127.0.0.1:7897 http_proxy=http://127.0.0.1:7897 all_proxy=socks5://127.0.0.1:7897
piexif # For EXIF metadata handling in JPEG files 
