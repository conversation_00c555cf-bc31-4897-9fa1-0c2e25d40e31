Jaaz Licensing Policy (Dual License: Community & Commercial)

Jaaz is released under a dual-license model: the Jaaz Community License (free) and a Commercial License (paid).

1. Jaaz Community License

Permitted Uses (Free):

Individuals (Personal Use):

Granted a free, perpetual license to use all standard Jaaz functions.

Content created using Jaaz may be used for personal or commercial projects.

Organizations (Limited Use):

Organizations may use Jaaz only in its standard, unmodified, out-of-the-box form for evaluation or non-commercial purposes.

Strictly Prohibited Without a Commercial License:

Team Deployment: Installing, hosting, or operating Jaaz for simultaneous use by multiple users in an organization (e.g., on shared servers, internal networks, or private clouds).

Code Modification or Derivative Works: Modifying, translating, reverse-engineering, or creating derivative products based on Jaaz source code.

Redistribution: Redistributing Jaaz (modified or unmodified) as part of another product or service.

Competitive Offering: Using Jaaz (in whole or in part) as the foundation of a competing commercial product.

2. Commercial License

A Commercial License is REQUIRED for any of the following:

Internal multi-user or team deployment.

Any secondary development, customization, or modification of the source code.

Embedding or redistributing <PERSON><PERSON>z as part of your own commercial offering.

Access to official technical support, warranties, or indemnification.

A Commercial License grants you the legal right to use Jaaz for enterprise purposes and to adapt its source code for your organization’s needs.

👉 To obtain a Commercial License, please contact: <EMAIL>

3. Contributions

By submitting contributions, you agree to grant the Jaaz maintainers a perpetual, worldwide, royalty-free, irrevocable license to use, modify, and sublicense your contributions under both the Community and Commercial Licenses.

4. Trademarks & UI

The Jaaz name, logo, and visual/interaction design are protected intellectual property and trademarks.

You may not use Jaaz branding, trademarks, or UI/UX designs for derivative or resale products without explicit permission.

© 2025 Jaaz Contributors & 11cafe Studio. All rights reserved.
Jaaz 许可协议（双重许可：社区版 & 商业版）

Jaaz 采用双重许可模式发布：Jaaz 社区许可证（免费） 和 商业许可证（付费）。

1. Jaaz 社区许可证（免费）

允许的使用：

个人用户（个人使用）：

免费、永久地使用 Jaaz 的所有标准功能。

使用 Jaaz 生成的内容可用于个人或商业项目。

组织用户（有限使用）：

组织仅可在 未经修改、原样的标准版本 下使用 Jaaz，用于评估或非商业目的。

在未获得商业许可证的情况下，严格禁止以下行为：

团队部署： 在组织内部以多人同时访问的方式安装、托管或运行 Jaaz（例如：共享服务器、内部网络或私有云环境）。

代码修改或衍生作品： 修改、翻译、逆向工程、反编译或基于 Jaaz 源代码创建衍生产品。

再分发： 将 Jaaz（无论是否修改）作为其他产品或服务的一部分进行再分发。

竞争性产品： 将 Jaaz（整体或部分）作为竞争性商业产品或服务的基础。

2. 商业许可证（付费）

以下任何情况 必须购买商业许可证：

内部多用户或团队部署。

对 Jaaz 源代码的任何二次开发、定制或修改。

将 Jaaz 嵌入或再分发为您自己的商业产品或服务的一部分。

获取官方的技术支持、质量保证或赔偿保障。

商业许可证为您提供在企业环境中合法使用 Jaaz 的权利，并允许根据组织需求修改其源代码。

👉 获取商业许可证，请联系：<EMAIL>

3. 贡献者条款

通过提交代码或其他贡献，您同意授予 Jaaz 项目维护者一项 永久的、全球性的、免费的、免版税的、不可撤销的许可，允许其在 社区许可证和商业许可证 下使用、修改和再许可您的贡献。

4. 商标与界面保护

Jaaz 名称、Logo 及其视觉/交互设计 受知识产权和商标法保护。

未经明确授权，不得将 Jaaz 的品牌、商标或 UI/UX 设计用于衍生或再销售的产品。

© 2025 Jaaz 贡献者 & 11cafe Studio. 版权所有。
