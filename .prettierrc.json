{"semi": false, "singleQuote": true, "arrowParens": "always", "printWidth": 100, "tabWidth": 2, "trailingComma": "es5", "bracketSpacing": true, "bracketSameLine": false, "endOfLine": "lf", "quoteProps": "as-needed", "jsxSingleQuote": true, "proseWrap": "preserve", "htmlWhitespaceSensitivity": "css", "vueIndentScriptAndStyle": false, "embeddedLanguageFormatting": "auto", "singleAttributePerLine": false, "useTabs": false, "insertPragma": false, "requirePragma": false, "overrides": [{"files": "*.md", "options": {"proseWrap": "always", "printWidth": 100}}, {"files": "*.json", "options": {"printWidth": 120}}, {"files": "*.yml", "options": {"printWidth": 120}}]}