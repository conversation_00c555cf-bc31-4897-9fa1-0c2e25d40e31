{"name": "@jaaz/agent-ui", "version": "0.0.0", "type": "module", "main": "dist/index.es.js", "module": "dist/index.es.js", "types": "dist/index.d.ts", "exports": {".": {"import": "./dist/index.es.js", "types": "./dist/index.d.ts"}, "./styles": "./dist/agent-ui.css"}, "files": ["dist"], "scripts": {"dev": "vite", "build": "tsc -b && vite build", "build:lib": "vite build --mode lib && cp src/index.d.ts dist/index.d.ts", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@excalidraw/excalidraw": "^0.18.0", "@mdxeditor/editor": "^3.32.2", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.1", "@radix-ui/react-context-menu": "^2.2.14", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.14", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.2", "@radix-ui/react-tooltip": "^1.1.8", "@tabler/icons-react": "^3.31.0", "@tailwindcss/vite": "^4.0.17", "@tanstack/query-async-storage-persister": "^5.81.5", "@tanstack/react-query": "^5.80.3", "@tanstack/react-query-persist-client": "^5.81.5", "@tanstack/react-router": "^1.120.15", "@tanstack/react-router-devtools": "^1.120.15", "@xyflow/react": "^12.7.0", "ahooks": "^3.8.5", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "file-saver": "^2.0.5", "i18next": "^25.2.1", "i18next-browser-languagedetector": "^8.1.0", "idb": "^8.0.3", "immer": "^10.1.1", "jszip": "^3.10.1", "lodash.debounce": "^4.0.8", "lucide-react": "^0.484.0", "mitt": "^3.0.1", "motion": "^12.16.0", "nanoid": "^5.1.5", "next-themes": "^0.4.6", "openai": "^4.98.0", "posthog-js": "^1.257.1", "rc-textarea": "^1.10.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-i18next": "^15.5.2", "react-markdown": "^10.1.0", "react-markdown-editor-lite": "^1.3.4", "react-photo-view": "^1.2.7", "react-resizable-panels": "^3.0.2", "remark-gfm": "^4.0.1", "socket.io-client": "^4.8.1", "sonner": "^2.0.3", "tailwind-merge": "^3.0.2", "tailwindcss": "^4.0.17", "tldraw": "^3.13.1", "tw-animate-css": "^1.2.4", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/js": "^9.21.0", "@tanstack/router-plugin": "^1.120.15", "@types/file-saver": "^2.0.7", "@types/lodash.debounce": "^4.0.9", "@types/node": "^22.13.13", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.21.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "prettier": "^3.3.2", "typescript": "~5.7.2", "typescript-eslint": "^8.24.1", "vite": "^6.2.0"}}