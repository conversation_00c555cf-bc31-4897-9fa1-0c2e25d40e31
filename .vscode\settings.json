{"python.languageServer": "<PERSON><PERSON><PERSON>", "python.analysis.typeCheckingMode": "strict", "python.analysis.extraPaths": ["server/"], "[python]": {"editor.defaultFormatter": "ms-python.black-formatter", "editor.formatOnSave": true}, "black-formatter.args": ["--config", "pyproject.toml"], "editor.formatOnSave": true, "prettier.enable": true, "python.analysis.autoImportCompletions": true, "i18n-ally.localesPaths": ["react/src/i18n/locales"], "i18n-ally.enabledFrameworks": ["react-i18next"], "i18n-ally.keystyle": "nested", "i18n-ally.namespace": true, "i18n-ally.pathMatcher": "{locale}/{namespaces}.json", "i18n-ally.displayLanguage": "zh-CN", "i18n-ally.sourceLanguage": "en", "python-envs.defaultEnvManager": "ms-python.python:system", "python-envs.pythonProjects": []}